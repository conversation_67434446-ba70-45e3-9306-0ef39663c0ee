package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"
	"website-monitor/database"
	"website-monitor/handlers"
	"website-monitor/models"
	"website-monitor/services"

	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

func main() {
	log.Println("🚀 Starting Website Monitor Server...")

	// Initialize database
	if err := database.InitDatabase(); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.CloseDatabase()

	// Create router
	router := mux.NewRouter()

	// Setup API routes
	setupAPIRoutes(router)

	// Setup static file serving
	setupStaticRoutes(router)

	// Setup CORS
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"*"},
	})

	handler := c<PERSON>(router)

	// Create HTTP server
	server := &http.Server{
		Addr:         ":8080",
		Handler:      handler,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start monitoring service
	monitorService := services.NewMonitorService()
	go startMonitoringLoop(monitorService)

	// Start server in a goroutine
	go func() {
		log.Printf("✅ Server starting on http://localhost%s", server.Addr)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start server: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("🛑 Shutting down server...")

	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("✅ Server exited")
}

// setupAPIRoutes configures all API endpoints
func setupAPIRoutes(router *mux.Router) {
	api := router.PathPrefix("/api").Subrouter()

	// Website endpoints
	websiteHandler := handlers.NewWebsiteHandler()
	api.HandleFunc("/websites", websiteHandler.CreateWebsite).Methods("POST")
	api.HandleFunc("/websites", websiteHandler.GetWebsites).Methods("GET")
	api.HandleFunc("/websites/{id}", websiteHandler.GetWebsite).Methods("GET")
	api.HandleFunc("/websites/{id}", websiteHandler.UpdateWebsite).Methods("PUT")
	api.HandleFunc("/websites/{id}", websiteHandler.DeleteWebsite).Methods("DELETE")

	// Monitoring endpoints
	monitorHandler := handlers.NewMonitorHandler()
	api.HandleFunc("/websites/{id}/checks", monitorHandler.GetWebsiteChecks).Methods("GET")
	api.HandleFunc("/websites/{id}/stats", monitorHandler.GetWebsiteStats).Methods("GET")
	api.HandleFunc("/websites/{id}/check", monitorHandler.TriggerCheck).Methods("POST")

	log.Println("✅ API routes configured")
}

// setupStaticRoutes configures static file serving
func setupStaticRoutes(router *mux.Router) {
	// Serve static files from frontend directory
	frontendDir := filepath.Join("..", "frontend")
	
	// Serve index.html at root
	router.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		http.ServeFile(w, r, filepath.Join(frontendDir, "index.html"))
	})

	// Serve static files
	router.PathPrefix("/").Handler(http.FileServer(http.Dir(frontendDir)))

	log.Println("✅ Static routes configured")
}

// startMonitoringLoop runs the continuous monitoring process
func startMonitoringLoop(monitorService *services.MonitorService) {
	log.Println("🔍 Starting monitoring loop...")

	ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			checkAllWebsites(monitorService)
		}
	}
}

// checkAllWebsites performs monitoring checks on all active websites
func checkAllWebsites(monitorService *services.MonitorService) {
	websites, err := models.GetAllWebsites()
	if err != nil {
		log.Printf("❌ Failed to get websites for monitoring: %v", err)
		return
	}

	if len(websites) == 0 {
		return
	}

	log.Printf("🔍 Checking %d websites...", len(websites))

	for _, website := range websites {
		// Check if it's time to monitor this website
		if shouldCheckWebsite(website) {
			go func(w *models.Website) {
				if err := monitorService.CheckAndStore(w); err != nil {
					log.Printf("❌ Failed to check website %s: %v", w.URL, err)
				} else {
					log.Printf("✅ Checked website %s", w.URL)
				}
			}(website)
		}
	}
}

// shouldCheckWebsite determines if a website should be checked based on its interval
func shouldCheckWebsite(website *models.Website) bool {
	// Get the last check for this website
	checks, err := models.GetChecksByWebsite(website.ID, 1)
	if err != nil || len(checks) == 0 {
		// No previous checks, should check now
		return true
	}

	lastCheck := checks[0]
	timeSinceLastCheck := time.Since(lastCheck.CheckedAt)
	checkInterval := time.Duration(website.CheckInterval) * time.Second

	return timeSinceLastCheck >= checkInterval
}
