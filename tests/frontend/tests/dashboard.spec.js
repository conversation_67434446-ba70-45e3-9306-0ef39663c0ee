const { test, expect } = require('@playwright/test');

test.describe('Website Monitor Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should display the main dashboard', async ({ page }) => {
    // Check page title
    await expect(page).toHaveTitle(/Website Monitor Dashboard/);
    
    // Check header elements
    await expect(page.locator('h1')).toContainText('Website Monitor');
    await expect(page.locator('#add-website-btn')).toBeVisible();
    await expect(page.locator('#refresh-btn')).toBeVisible();
    
    // Check summary cards
    await expect(page.locator('#total-up')).toBeVisible();
    await expect(page.locator('#total-down')).toBeVisible();
    await expect(page.locator('#avg-response')).toBeVisible();
    await expect(page.locator('#avg-uptime')).toBeVisible();
  });

  test('should show empty state when no websites', async ({ page }) => {
    // Should show no websites message
    await expect(page.locator('#no-websites')).toBeVisible();
    await expect(page.locator('#no-websites h3')).toContainText('No websites being monitored');
    
    // Websites grid should be hidden
    await expect(page.locator('#websites-grid')).toBeHidden();
  });

  test('should open add website modal', async ({ page }) => {
    // Click add website button
    await page.click('#add-website-btn');
    
    // Modal should be visible
    await expect(page.locator('#add-website-modal')).toBeVisible();
    await expect(page.locator('#add-website-modal h2')).toContainText('Add New Website');
    
    // Form fields should be present
    await expect(page.locator('#website-name')).toBeVisible();
    await expect(page.locator('#website-url')).toBeVisible();
    await expect(page.locator('#check-interval')).toBeVisible();
    await expect(page.locator('#alert-threshold')).toBeVisible();
  });

  test('should close modal when clicking cancel', async ({ page }) => {
    // Open modal
    await page.click('#add-website-btn');
    await expect(page.locator('#add-website-modal')).toBeVisible();
    
    // Click cancel
    await page.click('button:has-text("Cancel")');
    
    // Modal should be hidden
    await expect(page.locator('#add-website-modal')).toBeHidden();
  });

  test('should close modal when pressing escape', async ({ page }) => {
    // Open modal
    await page.click('#add-website-btn');
    await expect(page.locator('#add-website-modal')).toBeVisible();
    
    // Press escape
    await page.keyboard.press('Escape');
    
    // Modal should be hidden
    await expect(page.locator('#add-website-modal')).toBeHidden();
  });

  test('should add a new website', async ({ page }) => {
    // Open add website modal
    await page.click('#add-website-btn');
    
    // Fill form
    await page.fill('#website-name', 'Test Website');
    await page.fill('#website-url', 'https://httpbin.org/status/200');
    await page.selectOption('#check-interval', '60');
    await page.fill('#alert-threshold', '3000');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for modal to close
    await expect(page.locator('#add-website-modal')).toBeHidden();
    
    // Check that website appears in the grid
    await expect(page.locator('#websites-grid')).toBeVisible();
    await expect(page.locator('#no-websites')).toBeHidden();
    
    // Check website card content
    const websiteCard = page.locator('.website-card').first();
    await expect(websiteCard).toBeVisible();
    await expect(websiteCard.locator('h3')).toContainText('Test Website');
    await expect(websiteCard.locator('.website-url')).toContainText('https://httpbin.org/status/200');
  });

  test('should validate required fields', async ({ page }) => {
    // Open modal
    await page.click('#add-website-btn');
    
    // Try to submit empty form
    await page.click('button[type="submit"]');
    
    // Form should not submit (modal still visible)
    await expect(page.locator('#add-website-modal')).toBeVisible();
    
    // Check HTML5 validation
    const nameField = page.locator('#website-name');
    const urlField = page.locator('#website-url');
    
    await expect(nameField).toHaveAttribute('required');
    await expect(urlField).toHaveAttribute('required');
  });

  test('should validate URL format', async ({ page }) => {
    // Open modal
    await page.click('#add-website-btn');
    
    // Fill with invalid URL
    await page.fill('#website-name', 'Test Site');
    await page.fill('#website-url', 'not-a-valid-url');
    
    // Try to submit
    await page.click('button[type="submit"]');
    
    // Form should not submit due to URL validation
    await expect(page.locator('#add-website-modal')).toBeVisible();
  });

  test('should refresh data when clicking refresh button', async ({ page }) => {
    // Click refresh button
    await page.click('#refresh-btn');
    
    // Loading overlay should appear briefly
    await expect(page.locator('#loading-overlay')).toBeVisible();
    
    // Wait for loading to complete
    await expect(page.locator('#loading-overlay')).toBeHidden();
  });

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check that elements are still visible and properly arranged
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('#add-website-btn')).toBeVisible();
    await expect(page.locator('.summary-cards')).toBeVisible();
    
    // Summary cards should stack vertically on mobile
    const summaryCards = page.locator('.summary-card');
    const firstCard = summaryCards.first();
    const secondCard = summaryCards.nth(1);
    
    const firstCardBox = await firstCard.boundingBox();
    const secondCardBox = await secondCard.boundingBox();
    
    // Second card should be below first card (higher y position)
    expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y);
  });
});
