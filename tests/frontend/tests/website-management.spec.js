const { test, expect } = require('@playwright/test');

test.describe('Website Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
  });

  test('should add and display a website', async ({ page }) => {
    // Add a website
    await page.click('#add-website-btn');
    await page.fill('#website-name', 'Example Website');
    await page.fill('#website-url', 'https://httpbin.org/status/200');
    await page.selectOption('#check-interval', '300');
    await page.fill('#alert-threshold', '5000');
    await page.click('button[type="submit"]');
    
    // Wait for modal to close and website to appear
    await expect(page.locator('#add-website-modal')).toBeHidden();
    await expect(page.locator('#websites-grid')).toBeVisible();
    
    // Verify website card
    const websiteCard = page.locator('.website-card').first();
    await expect(websiteCard.locator('h3')).toContainText('Example Website');
    await expect(websiteCard.locator('.website-url')).toContainText('https://httpbin.org/status/200');
    
    // Check that summary cards are updated
    await expect(page.locator('#total-up')).not.toContainText('0');
  });

  test('should show website details modal', async ({ page }) => {
    // First add a website
    await page.click('#add-website-btn');
    await page.fill('#website-name', 'Detail Test Site');
    await page.fill('#website-url', 'https://httpbin.org/status/200');
    await page.click('button[type="submit"]');
    
    // Wait for website to appear
    await expect(page.locator('.website-card')).toBeVisible();
    
    // Click on website card
    await page.click('.website-card');
    
    // Details modal should open
    await expect(page.locator('#website-details-modal')).toBeVisible();
    await expect(page.locator('#details-website-name')).toContainText('Detail Test Site');
    
    // Check tabs are present
    await expect(page.locator('.tab-btn:has-text("Overview")')).toBeVisible();
    await expect(page.locator('.tab-btn:has-text("Recent Checks")')).toBeVisible();
    await expect(page.locator('.tab-btn:has-text("Settings")')).toBeVisible();
    
    // Overview tab should be active by default
    await expect(page.locator('.tab-btn:has-text("Overview")')).toHaveClass(/active/);
    await expect(page.locator('#overview-tab')).toHaveClass(/active/);
  });

  test('should switch between tabs in details modal', async ({ page }) => {
    // Add a website and open details
    await page.click('#add-website-btn');
    await page.fill('#website-name', 'Tab Test Site');
    await page.fill('#website-url', 'https://httpbin.org/status/200');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('.website-card')).toBeVisible();
    await page.click('.website-card');
    await expect(page.locator('#website-details-modal')).toBeVisible();
    
    // Click on Recent Checks tab
    await page.click('.tab-btn:has-text("Recent Checks")');
    
    // Recent Checks tab should be active
    await expect(page.locator('.tab-btn:has-text("Recent Checks")')).toHaveClass(/active/);
    await expect(page.locator('#checks-tab')).toHaveClass(/active/);
    await expect(page.locator('#overview-tab')).not.toHaveClass(/active/);
    
    // Click on Settings tab
    await page.click('.tab-btn:has-text("Settings")');
    
    // Settings tab should be active
    await expect(page.locator('.tab-btn:has-text("Settings")')).toHaveClass(/active/);
    await expect(page.locator('#settings-tab')).toHaveClass(/active/);
    await expect(page.locator('#checks-tab')).not.toHaveClass(/active/);
  });

  test('should close website details modal', async ({ page }) => {
    // Add a website and open details
    await page.click('#add-website-btn');
    await page.fill('#website-name', 'Close Test Site');
    await page.fill('#website-url', 'https://httpbin.org/status/200');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('.website-card')).toBeVisible();
    await page.click('.website-card');
    await expect(page.locator('#website-details-modal')).toBeVisible();
    
    // Close modal using close button
    await page.click('#website-details-modal .modal-close');
    await expect(page.locator('#website-details-modal')).toBeHidden();
    
    // Open again and close with escape key
    await page.click('.website-card');
    await expect(page.locator('#website-details-modal')).toBeVisible();
    await page.keyboard.press('Escape');
    await expect(page.locator('#website-details-modal')).toBeHidden();
  });

  test('should handle multiple websites', async ({ page }) => {
    // Add multiple websites
    const websites = [
      { name: 'Site 1', url: 'https://httpbin.org/status/200' },
      { name: 'Site 2', url: 'https://httpbin.org/status/201' },
      { name: 'Site 3', url: 'https://httpbin.org/status/202' }
    ];
    
    for (const website of websites) {
      await page.click('#add-website-btn');
      await page.fill('#website-name', website.name);
      await page.fill('#website-url', website.url);
      await page.click('button[type="submit"]');
      await expect(page.locator('#add-website-modal')).toBeHidden();
    }
    
    // Check that all websites are displayed
    await expect(page.locator('.website-card')).toHaveCount(3);
    
    // Check that each website has correct name
    for (let i = 0; i < websites.length; i++) {
      await expect(page.locator('.website-card').nth(i).locator('h3')).toContainText(websites[i].name);
    }
  });

  test('should show toast notifications', async ({ page }) => {
    // Add a website to trigger success toast
    await page.click('#add-website-btn');
    await page.fill('#website-name', 'Toast Test Site');
    await page.fill('#website-url', 'https://httpbin.org/status/200');
    await page.click('button[type="submit"]');
    
    // Success toast should appear
    await expect(page.locator('.toast.success')).toBeVisible();
    await expect(page.locator('.toast.success')).toContainText('added successfully');
    
    // Toast should auto-disappear after a few seconds
    await page.waitForTimeout(6000);
    await expect(page.locator('.toast.success')).toBeHidden();
  });

  test('should handle network errors gracefully', async ({ page }) => {
    // Mock network failure for API call
    await page.route('/api/websites', route => {
      route.abort('failed');
    });
    
    // Try to add a website
    await page.click('#add-website-btn');
    await page.fill('#website-name', 'Error Test Site');
    await page.fill('#website-url', 'https://httpbin.org/status/200');
    await page.click('button[type="submit"]');
    
    // Error toast should appear
    await expect(page.locator('.toast.error')).toBeVisible();
    await expect(page.locator('.toast.error')).toContainText('Failed to add website');
  });

  test('should auto-refresh data', async ({ page }) => {
    // Add a website first
    await page.click('#add-website-btn');
    await page.fill('#website-name', 'Auto Refresh Test');
    await page.fill('#website-url', 'https://httpbin.org/status/200');
    await page.click('button[type="submit"]');
    
    await expect(page.locator('.website-card')).toBeVisible();
    
    // Wait for auto-refresh (30 seconds is too long for test, so we'll trigger manually)
    await page.click('#refresh-btn');
    
    // Loading should appear and disappear
    await expect(page.locator('#loading-overlay')).toBeVisible();
    await expect(page.locator('#loading-overlay')).toBeHidden();
    
    // Website should still be there
    await expect(page.locator('.website-card')).toBeVisible();
  });

  test('should validate form inputs', async ({ page }) => {
    await page.click('#add-website-btn');
    
    // Test name validation
    await page.fill('#website-url', 'https://example.com');
    await page.click('button[type="submit"]');
    await expect(page.locator('#add-website-modal')).toBeVisible(); // Should stay open
    
    // Test URL validation
    await page.fill('#website-name', 'Test Site');
    await page.fill('#website-url', 'invalid-url');
    await page.click('button[type="submit"]');
    await expect(page.locator('#add-website-modal')).toBeVisible(); // Should stay open
    
    // Test valid form
    await page.fill('#website-url', 'https://example.com');
    await page.click('button[type="submit"]');
    await expect(page.locator('#add-website-modal')).toBeHidden(); // Should close
  });
});
