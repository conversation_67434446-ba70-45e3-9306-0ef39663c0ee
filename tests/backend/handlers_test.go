package main

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"website-monitor/handlers"
	"website-monitor/models"

	"github.com/gorilla/mux"
)

func TestCreateWebsiteHandler(t *testing.T) {
	// Create request body
	website := map[string]interface{}{
		"name":            "Test API Site",
		"url":             "https://api-test.com",
		"check_interval":  300,
		"alert_threshold": 5000,
	}

	jsonData, _ := json.Marshal(website)
	req := httptest.NewRequest("POST", "/api/websites", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	rr := httptest.NewRecorder()

	// Call handler
	handler := handlers.NewWebsiteHandler()
	handler.CreateWebsite(rr, req)

	// Check status code
	if status := rr.Code; status != http.StatusCreated {
		t.Errorf("Expected status code %d, got %d", http.StatusCreated, status)
	}

	// Check response body
	var response models.Website
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response.Name != "Test API Site" {
		t.Errorf("Expected name 'Test API Site', got %s", response.Name)
	}

	if response.ID == 0 {
		t.Error("Expected website ID to be set")
	}
}

func TestGetWebsitesHandler(t *testing.T) {
	// Create test websites
	websites := []*models.Website{
		{Name: "API Site 1", URL: "https://api1.com", CheckInterval: 300, AlertThreshold: 5000},
		{Name: "API Site 2", URL: "https://api2.com", CheckInterval: 600, AlertThreshold: 3000},
	}

	for _, website := range websites {
		err := website.Create()
		if err != nil {
			t.Fatalf("Failed to create website: %v", err)
		}
	}

	// Create request
	req := httptest.NewRequest("GET", "/api/websites", nil)
	rr := httptest.NewRecorder()

	// Call handler
	handler := handlers.NewWebsiteHandler()
	handler.GetWebsites(rr, req)

	// Check status code
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, status)
	}

	// Check response body
	var response []*models.Website
	err := json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if len(response) < 2 {
		t.Errorf("Expected at least 2 websites, got %d", len(response))
	}
}

func TestGetWebsiteHandler(t *testing.T) {
	// Create test website
	website := &models.Website{
		Name:           "Get API Site",
		URL:            "https://get-api.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Create request
	req := httptest.NewRequest("GET", "/api/websites/"+strconv.Itoa(website.ID), nil)
	rr := httptest.NewRecorder()

	// Setup router with path variable
	router := mux.NewRouter()
	handler := handlers.NewWebsiteHandler()
	router.HandleFunc("/api/websites/{id}", handler.GetWebsite).Methods("GET")
	router.ServeHTTP(rr, req)

	// Check status code
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, status)
	}

	// Check response body
	var response models.Website
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response.ID != website.ID {
		t.Errorf("Expected website ID %d, got %d", website.ID, response.ID)
	}
}

func TestUpdateWebsiteHandler(t *testing.T) {
	// Create test website
	website := &models.Website{
		Name:           "Update API Site",
		URL:            "https://update-api.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Create update request
	updateData := map[string]interface{}{
		"name":            "Updated API Site",
		"url":             "https://updated-api.com",
		"check_interval":  600,
		"alert_threshold": 3000,
	}

	jsonData, _ := json.Marshal(updateData)
	req := httptest.NewRequest("PUT", "/api/websites/"+strconv.Itoa(website.ID), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	rr := httptest.NewRecorder()

	// Setup router with path variable
	router := mux.NewRouter()
	handler := handlers.NewWebsiteHandler()
	router.HandleFunc("/api/websites/{id}", handler.UpdateWebsite).Methods("PUT")
	router.ServeHTTP(rr, req)

	// Check status code
	if status := rr.Code; status != http.StatusOK {
		t.Errorf("Expected status code %d, got %d", http.StatusOK, status)
	}

	// Check response body
	var response models.Website
	err = json.Unmarshal(rr.Body.Bytes(), &response)
	if err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}

	if response.Name != "Updated API Site" {
		t.Errorf("Expected name 'Updated API Site', got %s", response.Name)
	}
}

func TestDeleteWebsiteHandler(t *testing.T) {
	// Create test website
	website := &models.Website{
		Name:           "Delete API Site",
		URL:            "https://delete-api.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Create delete request
	req := httptest.NewRequest("DELETE", "/api/websites/"+strconv.Itoa(website.ID), nil)
	rr := httptest.NewRecorder()

	// Setup router with path variable
	router := mux.NewRouter()
	handler := handlers.NewWebsiteHandler()
	router.HandleFunc("/api/websites/{id}", handler.DeleteWebsite).Methods("DELETE")
	router.ServeHTTP(rr, req)

	// Check status code
	if status := rr.Code; status != http.StatusNoContent {
		t.Errorf("Expected status code %d, got %d", http.StatusNoContent, status)
	}

	// Verify website is deleted
	_, err = models.GetWebsite(website.ID)
	if err == nil {
		t.Error("Expected error when getting deleted website")
	}
}
