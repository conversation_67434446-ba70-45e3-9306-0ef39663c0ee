package main

import (
	"os"
	"testing"
	"website-monitor/database"
	"website-monitor/models"
)

func TestMain(m *testing.M) {
	// Setup test database
	os.Setenv("DB_PATH", ":memory:")
	if err := database.InitDatabase(); err != nil {
		panic(err)
	}

	// Run tests
	code := m.Run()

	// Cleanup
	database.CloseDatabase()
	os.Exit(code)
}

func TestCreateWebsite(t *testing.T) {
	website := &models.Website{
		Name:           "Test Site",
		URL:            "https://example.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	if website.ID == 0 {
		t.Error("Expected website ID to be set after creation")
	}

	if website.CreatedAt.IsZero() {
		t.Error("Expected CreatedAt to be set")
	}
}

func TestGetWebsite(t *testing.T) {
	// Create a test website
	website := &models.Website{
		Name:           "Get Test Site",
		URL:            "https://gettest.com",
		CheckInterval:  600,
		AlertThreshold: 3000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Retrieve the website
	retrieved, err := models.GetWebsite(website.ID)
	if err != nil {
		t.Fatalf("Failed to get website: %v", err)
	}

	if retrieved.Name != website.Name {
		t.Errorf("Expected name %s, got %s", website.Name, retrieved.Name)
	}

	if retrieved.URL != website.URL {
		t.Errorf("Expected URL %s, got %s", website.URL, retrieved.URL)
	}
}

func TestGetAllWebsites(t *testing.T) {
	// Create multiple test websites
	websites := []*models.Website{
		{Name: "Site 1", URL: "https://site1.com", CheckInterval: 300, AlertThreshold: 5000},
		{Name: "Site 2", URL: "https://site2.com", CheckInterval: 600, AlertThreshold: 3000},
	}

	for _, website := range websites {
		err := website.Create()
		if err != nil {
			t.Fatalf("Failed to create website: %v", err)
		}
	}

	// Retrieve all websites
	allWebsites, err := models.GetAllWebsites()
	if err != nil {
		t.Fatalf("Failed to get all websites: %v", err)
	}

	if len(allWebsites) < 2 {
		t.Errorf("Expected at least 2 websites, got %d", len(allWebsites))
	}
}

func TestUpdateWebsite(t *testing.T) {
	// Create a test website
	website := &models.Website{
		Name:           "Update Test Site",
		URL:            "https://updatetest.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Update the website
	website.Name = "Updated Site Name"
	website.CheckInterval = 900

	err = website.Update()
	if err != nil {
		t.Fatalf("Failed to update website: %v", err)
	}

	// Verify the update
	updated, err := models.GetWebsite(website.ID)
	if err != nil {
		t.Fatalf("Failed to get updated website: %v", err)
	}

	if updated.Name != "Updated Site Name" {
		t.Errorf("Expected updated name 'Updated Site Name', got %s", updated.Name)
	}

	if updated.CheckInterval != 900 {
		t.Errorf("Expected updated check interval 900, got %d", updated.CheckInterval)
	}
}

func TestDeleteWebsite(t *testing.T) {
	// Create a test website
	website := &models.Website{
		Name:           "Delete Test Site",
		URL:            "https://deletetest.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	websiteID := website.ID

	// Delete the website
	err = website.Delete()
	if err != nil {
		t.Fatalf("Failed to delete website: %v", err)
	}

	// Verify deletion
	_, err = models.GetWebsite(websiteID)
	if err == nil {
		t.Error("Expected error when getting deleted website")
	}
}
