package main

import (
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"website-monitor/models"
	"website-monitor/services"
)

func TestCheckWebsite(t *testing.T) {
	// Create a test HTTP server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(50 * time.Millisecond) // Simulate some response time
		w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
		w.Write([]byte("OK"))
	}))
	defer server.Close()

	// Create a test website
	website := &models.Website{
		Name:           "Test Server",
		URL:            server.URL,
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Test the monitoring service
	monitor := services.NewMonitorService()
	check, err := monitor.CheckWebsite(website)
	if err != nil {
		t.Fatalf("Failed to check website: %v", err)
	}

	// Verify the check results
	if check.WebsiteID != website.ID {
		t.Errorf("Expected website ID %d, got %d", website.ID, check.WebsiteID)
	}

	if check.StatusCode != 200 {
		t.<PERSON>rrorf("Expected status code 200, got %d", check.StatusCode)
	}

	if !check.IsUp {
		t.Error("Expected website to be up")
	}

	if check.ResponseTime <= 0 {
		t.Error("Expected positive response time")
	}

	if check.ResponseTime < 40 || check.ResponseTime > 200 {
		t.Errorf("Expected response time around 50ms, got %dms", check.ResponseTime)
	}
}

func TestCheckWebsiteDown(t *testing.T) {
	// Create a test HTTP server that returns 500
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte("Internal Server Error"))
	}))
	defer server.Close()

	// Create a test website
	website := &models.Website{
		Name:           "Test Server Down",
		URL:            server.URL,
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Test the monitoring service
	monitor := services.NewMonitorService()
	check, err := monitor.CheckWebsite(website)
	if err != nil {
		t.Fatalf("Failed to check website: %v", err)
	}

	// Verify the check results
	if check.StatusCode != 500 {
		t.Errorf("Expected status code 500, got %d", check.StatusCode)
	}

	if check.IsUp {
		t.Error("Expected website to be down")
	}
}

func TestCheckWebsiteTimeout(t *testing.T) {
	// Create a test HTTP server that times out
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		time.Sleep(2 * time.Second) // Longer than our timeout
		w.WriteHeader(http.StatusOK)
	}))
	defer server.Close()

	// Create a test website
	website := &models.Website{
		Name:           "Test Server Timeout",
		URL:            server.URL,
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Test the monitoring service with short timeout
	monitor := services.NewMonitorServiceWithTimeout(1 * time.Second)
	check, err := monitor.CheckWebsite(website)
	if err != nil {
		t.Fatalf("Failed to check website: %v", err)
	}

	// Verify the check results
	if check.IsUp {
		t.Error("Expected website to be down due to timeout")
	}

	if check.ErrorMessage == "" {
		t.Error("Expected error message for timeout")
	}
}

func TestCheckWebsiteInvalidURL(t *testing.T) {
	// Create a test website with invalid URL
	website := &models.Website{
		Name:           "Invalid URL",
		URL:            "http://invalid-url-that-does-not-exist.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}

	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Test the monitoring service
	monitor := services.NewMonitorService()
	check, err := monitor.CheckWebsite(website)
	if err != nil {
		t.Fatalf("Failed to check website: %v", err)
	}

	// Verify the check results
	if check.IsUp {
		t.Error("Expected website to be down due to invalid URL")
	}

	if check.ErrorMessage == "" {
		t.Error("Expected error message for invalid URL")
	}

	if check.StatusCode != 0 {
		t.Errorf("Expected status code 0 for failed request, got %d", check.StatusCode)
	}
}
