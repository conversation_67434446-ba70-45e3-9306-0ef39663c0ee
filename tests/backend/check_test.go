package main

import (
	"testing"
	"time"
	"website-monitor/models"
)

func TestCreateCheck(t *testing.T) {
	// Create a test website first
	website := &models.Website{
		Name:           "Check Test Site",
		URL:            "https://checktest.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}
	
	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Create a check
	check := &models.Check{
		WebsiteID:    website.ID,
		StatusCode:   200,
		ResponseTime: 150,
		IsUp:         true,
	}

	err = check.Create()
	if err != nil {
		t.Fatalf("Failed to create check: %v", err)
	}

	if check.ID == 0 {
		t.Error("Expected check ID to be set after creation")
	}

	if check.CheckedAt.IsZero() {
		t.Error("Expected CheckedAt to be set")
	}
}

func TestGetChecksByWebsite(t *testing.T) {
	// Create a test website
	website := &models.Website{
		Name:           "Checks Test Site",
		URL:            "https://checkstest.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}
	
	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Create multiple checks
	checks := []*models.Check{
		{WebsiteID: website.ID, StatusCode: 200, ResponseTime: 100, IsUp: true},
		{WebsiteID: website.ID, StatusCode: 200, ResponseTime: 200, IsUp: true},
		{WebsiteID: website.ID, StatusCode: 500, ResponseTime: 0, IsUp: false, ErrorMessage: "Server error"},
	}

	for _, check := range checks {
		err := check.Create()
		if err != nil {
			t.Fatalf("Failed to create check: %v", err)
		}
	}

	// Retrieve checks for the website
	retrievedChecks, err := models.GetChecksByWebsite(website.ID, 10)
	if err != nil {
		t.Fatalf("Failed to get checks: %v", err)
	}

	if len(retrievedChecks) != 3 {
		t.Errorf("Expected 3 checks, got %d", len(retrievedChecks))
	}

	// Verify the checks are ordered by checked_at DESC (most recent first)
	if len(retrievedChecks) >= 2 {
		if retrievedChecks[0].CheckedAt.Before(retrievedChecks[1].CheckedAt) {
			t.Error("Expected checks to be ordered by checked_at DESC")
		}
	}
}

func TestGetWebsiteStats(t *testing.T) {
	// Create a test website
	website := &models.Website{
		Name:           "Stats Test Site",
		URL:            "https://statstest.com",
		CheckInterval:  300,
		AlertThreshold: 5000,
	}
	
	err := website.Create()
	if err != nil {
		t.Fatalf("Failed to create website: %v", err)
	}

	// Create checks with known data for stats calculation
	checks := []*models.Check{
		{WebsiteID: website.ID, StatusCode: 200, ResponseTime: 100, IsUp: true},
		{WebsiteID: website.ID, StatusCode: 200, ResponseTime: 200, IsUp: true},
		{WebsiteID: website.ID, StatusCode: 200, ResponseTime: 300, IsUp: true},
		{WebsiteID: website.ID, StatusCode: 500, ResponseTime: 0, IsUp: false},
	}

	for _, check := range checks {
		err := check.Create()
		if err != nil {
			t.Fatalf("Failed to create check: %v", err)
		}
	}

	// Get stats for the last 24 hours
	stats, err := models.GetWebsiteStats(website.ID, time.Now().Add(-24*time.Hour), time.Now())
	if err != nil {
		t.Fatalf("Failed to get website stats: %v", err)
	}

	// Verify uptime percentage (3 out of 4 checks were up = 75%)
	expectedUptime := 75.0
	if stats.UptimePercentage != expectedUptime {
		t.Errorf("Expected uptime percentage %.1f, got %.1f", expectedUptime, stats.UptimePercentage)
	}

	// Verify average response time (100+200+300)/3 = 200ms (excluding failed check)
	expectedAvgResponse := 200.0
	if stats.AvgResponseTime != expectedAvgResponse {
		t.Errorf("Expected average response time %.1f, got %.1f", expectedAvgResponse, stats.AvgResponseTime)
	}

	// Verify total checks
	if stats.TotalChecks != 4 {
		t.Errorf("Expected 4 total checks, got %d", stats.TotalChecks)
	}
}
