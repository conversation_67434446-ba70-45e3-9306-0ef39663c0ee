package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"
	"website-monitor/models"
	"website-monitor/services"

	"github.com/gorilla/mux"
)

// MonitorHandler handles HTTP requests for monitoring operations
type MonitorHandler struct {
	monitorService *services.MonitorService
}

// NewMonitorHandler creates a new monitor handler instance
func NewMonitorHandler() *MonitorHandler {
	return &MonitorHandler{
		monitorService: services.NewMonitorService(),
	}
}

// GetWebsiteChecks handles GET /api/websites/{id}/checks
func (h *MonitorHandler) GetWebsiteChecks(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr, exists := vars["id"]
	if !exists {
		http.Error(w, "Website ID is required", http.StatusBadRequest)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		http.Error(w, "Invalid website ID", http.StatusBadRequest)
		return
	}

	// Get limit from query parameter (default: 50)
	limitStr := r.URL.Query().Get("limit")
	limit := 50
	if limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	// Verify website exists
	_, err = models.GetWebsite(id)
	if err != nil {
		http.Error(w, fmt.Sprintf("Website not found: %v", err), http.StatusNotFound)
		return
	}

	// Get checks
	checks, err := models.GetChecksByWebsite(id, limit)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get checks: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(checks)
}

// GetWebsiteStats handles GET /api/websites/{id}/stats
func (h *MonitorHandler) GetWebsiteStats(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr, exists := vars["id"]
	if !exists {
		http.Error(w, "Website ID is required", http.StatusBadRequest)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		http.Error(w, "Invalid website ID", http.StatusBadRequest)
		return
	}

	// Verify website exists
	_, err = models.GetWebsite(id)
	if err != nil {
		http.Error(w, fmt.Sprintf("Website not found: %v", err), http.StatusNotFound)
		return
	}

	// Get time range from query parameters (default: last 24 hours)
	hoursStr := r.URL.Query().Get("hours")
	hours := 24
	if hoursStr != "" {
		if parsedHours, err := strconv.Atoi(hoursStr); err == nil && parsedHours > 0 {
			hours = parsedHours
		}
	}

	endTime := time.Now()
	startTime := endTime.Add(-time.Duration(hours) * time.Hour)

	// Get stats
	stats, err := models.GetWebsiteStats(id, startTime, endTime)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get stats: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// TriggerCheck handles POST /api/websites/{id}/check
func (h *MonitorHandler) TriggerCheck(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr, exists := vars["id"]
	if !exists {
		http.Error(w, "Website ID is required", http.StatusBadRequest)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		http.Error(w, "Invalid website ID", http.StatusBadRequest)
		return
	}

	// Get website
	website, err := models.GetWebsite(id)
	if err != nil {
		http.Error(w, fmt.Sprintf("Website not found: %v", err), http.StatusNotFound)
		return
	}

	// Perform check
	check, err := h.monitorService.CheckWebsite(website)
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to check website: %v", err), http.StatusInternalServerError)
		return
	}

	// Store check result
	if err := check.Create(); err != nil {
		http.Error(w, fmt.Sprintf("Failed to store check result: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(check)
}
