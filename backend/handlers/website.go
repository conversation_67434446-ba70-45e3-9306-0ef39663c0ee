package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"website-monitor/models"

	"github.com/gorilla/mux"
)

// WebsiteHandler handles HTTP requests for website operations
type WebsiteHandler struct{}

// NewWebsiteHandler creates a new website handler instance
func NewWebsiteHandler() *WebsiteHandler {
	return &WebsiteHandler{}
}

// CreateWebsite handles POST /api/websites
func (h *WebsiteHandler) CreateWebsite(w http.ResponseWriter, r *http.Request) {
	var website models.Website
	
	// Decode JSON request body
	if err := json.NewDecoder(r.Body).Decode(&website); err != nil {
		http.Error(w, fmt.Sprintf("Invalid JSON: %v", err), http.StatusBadRequest)
		return
	}

	// Validate required fields
	if website.Name == "" {
		http.Error(w, "Name is required", http.StatusBadRequest)
		return
	}

	if website.URL == "" {
		http.Error(w, "URL is required", http.StatusBadRequest)
		return
	}

	// Validate URL format
	if _, err := url.ParseRequestURI(website.URL); err != nil {
		http.Error(w, "Invalid URL format", http.StatusBadRequest)
		return
	}

	// Set defaults if not provided
	if website.CheckInterval == 0 {
		website.CheckInterval = 300 // 5 minutes default
	}

	if website.AlertThreshold == 0 {
		website.AlertThreshold = 5000 // 5 seconds default
	}

	// Create website in database
	if err := website.Create(); err != nil {
		http.Error(w, fmt.Sprintf("Failed to create website: %v", err), http.StatusInternalServerError)
		return
	}

	// Return created website
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(website)
}

// GetWebsites handles GET /api/websites
func (h *WebsiteHandler) GetWebsites(w http.ResponseWriter, r *http.Request) {
	websites, err := models.GetAllWebsites()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get websites: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(websites)
}

// GetWebsite handles GET /api/websites/{id}
func (h *WebsiteHandler) GetWebsite(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr, exists := vars["id"]
	if !exists {
		http.Error(w, "Website ID is required", http.StatusBadRequest)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		http.Error(w, "Invalid website ID", http.StatusBadRequest)
		return
	}

	website, err := models.GetWebsite(id)
	if err != nil {
		http.Error(w, fmt.Sprintf("Website not found: %v", err), http.StatusNotFound)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(website)
}

// UpdateWebsite handles PUT /api/websites/{id}
func (h *WebsiteHandler) UpdateWebsite(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr, exists := vars["id"]
	if !exists {
		http.Error(w, "Website ID is required", http.StatusBadRequest)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		http.Error(w, "Invalid website ID", http.StatusBadRequest)
		return
	}

	// Get existing website
	website, err := models.GetWebsite(id)
	if err != nil {
		http.Error(w, fmt.Sprintf("Website not found: %v", err), http.StatusNotFound)
		return
	}

	// Decode update data
	var updateData models.Website
	if err := json.NewDecoder(r.Body).Decode(&updateData); err != nil {
		http.Error(w, fmt.Sprintf("Invalid JSON: %v", err), http.StatusBadRequest)
		return
	}

	// Update fields
	if updateData.Name != "" {
		website.Name = updateData.Name
	}
	if updateData.URL != "" {
		// Validate URL format
		if _, err := url.ParseRequestURI(updateData.URL); err != nil {
			http.Error(w, "Invalid URL format", http.StatusBadRequest)
			return
		}
		website.URL = updateData.URL
	}
	if updateData.CheckInterval > 0 {
		website.CheckInterval = updateData.CheckInterval
	}
	if updateData.AlertThreshold > 0 {
		website.AlertThreshold = updateData.AlertThreshold
	}

	// Save changes
	if err := website.Update(); err != nil {
		http.Error(w, fmt.Sprintf("Failed to update website: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(website)
}

// DeleteWebsite handles DELETE /api/websites/{id}
func (h *WebsiteHandler) DeleteWebsite(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr, exists := vars["id"]
	if !exists {
		http.Error(w, "Website ID is required", http.StatusBadRequest)
		return
	}

	id, err := strconv.Atoi(idStr)
	if err != nil {
		http.Error(w, "Invalid website ID", http.StatusBadRequest)
		return
	}

	// Get existing website
	website, err := models.GetWebsite(id)
	if err != nil {
		http.Error(w, fmt.Sprintf("Website not found: %v", err), http.StatusNotFound)
		return
	}

	// Delete website
	if err := website.Delete(); err != nil {
		http.Error(w, fmt.Sprintf("Failed to delete website: %v", err), http.StatusInternalServerError)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}
