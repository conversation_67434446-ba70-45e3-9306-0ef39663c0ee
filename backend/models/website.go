package models

import (
	"database/sql"
	"fmt"
	"time"
	"website-monitor/database"
)

// Website represents a monitored website
type Website struct {
	ID             int       `json:"id"`
	Name           string    `json:"name"`
	URL            string    `json:"url"`
	CheckInterval  int       `json:"check_interval"`  // in seconds
	AlertThreshold int       `json:"alert_threshold"` // in milliseconds
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	IsActive       bool      `json:"is_active"`
}

// Create inserts a new website into the database
func (w *Website) Create() error {
	query := `
		INSERT INTO websites (name, url, check_interval, alert_threshold, is_active)
		VALUES (?, ?, ?, ?, ?)
	`

	result, err := database.DB.Exec(query, w.Name, w.URL, w.CheckInterval, w.AlertThreshold, true)
	if err != nil {
		return fmt.Errorf("failed to create website: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %v", err)
	}

	w.ID = int(id)
	w.IsActive = true
	w.CreatedAt = time.Now()
	w.UpdatedAt = time.Now()

	return nil
}

// GetWebsite retrieves a website by ID
func GetWebsite(id int) (*Website, error) {
	website := &Website{}
	query := `
		SELECT id, name, url, check_interval, alert_threshold, created_at, updated_at, is_active
		FROM websites WHERE id = ? AND is_active = 1
	`

	err := database.DB.QueryRow(query, id).Scan(
		&website.ID,
		&website.Name,
		&website.URL,
		&website.CheckInterval,
		&website.AlertThreshold,
		&website.CreatedAt,
		&website.UpdatedAt,
		&website.IsActive,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("website with ID %d not found", id)
		}
		return nil, fmt.Errorf("failed to get website: %v", err)
	}

	return website, nil
}

// GetAllWebsites retrieves all active websites
func GetAllWebsites() ([]*Website, error) {
	query := `
		SELECT id, name, url, check_interval, alert_threshold, created_at, updated_at, is_active
		FROM websites WHERE is_active = 1
		ORDER BY created_at DESC
	`

	rows, err := database.DB.Query(query)
	if err != nil {
		return nil, fmt.Errorf("failed to query websites: %v", err)
	}
	defer rows.Close()

	var websites []*Website
	for rows.Next() {
		website := &Website{}
		err := rows.Scan(
			&website.ID,
			&website.Name,
			&website.URL,
			&website.CheckInterval,
			&website.AlertThreshold,
			&website.CreatedAt,
			&website.UpdatedAt,
			&website.IsActive,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan website: %v", err)
		}
		websites = append(websites, website)
	}

	return websites, nil
}

// Update modifies an existing website
func (w *Website) Update() error {
	query := `
		UPDATE websites 
		SET name = ?, url = ?, check_interval = ?, alert_threshold = ?, updated_at = CURRENT_TIMESTAMP
		WHERE id = ?
	`

	_, err := database.DB.Exec(query, w.Name, w.URL, w.CheckInterval, w.AlertThreshold, w.ID)
	if err != nil {
		return fmt.Errorf("failed to update website: %v", err)
	}

	w.UpdatedAt = time.Now()
	return nil
}

// Delete removes a website (soft delete by setting is_active to false)
func (w *Website) Delete() error {
	query := `UPDATE websites SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?`

	_, err := database.DB.Exec(query, w.ID)
	if err != nil {
		return fmt.Errorf("failed to delete website: %v", err)
	}

	w.IsActive = false
	w.UpdatedAt = time.Now()
	return nil
}
