package models

import (
	"fmt"
	"time"
	"website-monitor/database"
)

// Check represents a monitoring check result
type Check struct {
	ID           int       `json:"id"`
	WebsiteID    int       `json:"website_id"`
	StatusCode   int       `json:"status_code"`
	ResponseTime int       `json:"response_time"` // in milliseconds
	IsUp         bool      `json:"is_up"`
	ErrorMessage string    `json:"error_message,omitempty"`
	CheckedAt    time.Time `json:"checked_at"`
}

// WebsiteStats represents aggregated statistics for a website
type WebsiteStats struct {
	WebsiteID         int     `json:"website_id"`
	UptimePercentage  float64 `json:"uptime_percentage"`
	AvgResponseTime   float64 `json:"avg_response_time"`
	TotalChecks       int     `json:"total_checks"`
	SuccessfulChecks  int     `json:"successful_checks"`
	FailedChecks      int     `json:"failed_checks"`
	LastCheck         *Check  `json:"last_check,omitempty"`
}

// Create inserts a new check into the database
func (c *Check) Create() error {
	query := `
		INSERT INTO checks (website_id, status_code, response_time, is_up, error_message)
		VALUES (?, ?, ?, ?, ?)
	`
	
	result, err := database.DB.Exec(query, c.WebsiteID, c.StatusCode, c.ResponseTime, c.IsUp, c.ErrorMessage)
	if err != nil {
		return fmt.Errorf("failed to create check: %v", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %v", err)
	}

	c.ID = int(id)
	c.CheckedAt = time.Now()

	return nil
}

// GetChecksByWebsite retrieves the most recent checks for a website
func GetChecksByWebsite(websiteID int, limit int) ([]*Check, error) {
	query := `
		SELECT id, website_id, status_code, response_time, is_up, error_message, checked_at
		FROM checks 
		WHERE website_id = ?
		ORDER BY checked_at DESC
		LIMIT ?
	`
	
	rows, err := database.DB.Query(query, websiteID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to query checks: %v", err)
	}
	defer rows.Close()

	var checks []*Check
	for rows.Next() {
		check := &Check{}
		err := rows.Scan(
			&check.ID,
			&check.WebsiteID,
			&check.StatusCode,
			&check.ResponseTime,
			&check.IsUp,
			&check.ErrorMessage,
			&check.CheckedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan check: %v", err)
		}
		checks = append(checks, check)
	}

	return checks, nil
}

// GetWebsiteStats calculates statistics for a website within a time range
func GetWebsiteStats(websiteID int, startTime, endTime time.Time) (*WebsiteStats, error) {
	stats := &WebsiteStats{
		WebsiteID: websiteID,
	}

	// Get total checks and uptime percentage
	uptimeQuery := `
		SELECT 
			COUNT(*) as total_checks,
			SUM(CASE WHEN is_up = 1 THEN 1 ELSE 0 END) as successful_checks,
			SUM(CASE WHEN is_up = 0 THEN 1 ELSE 0 END) as failed_checks
		FROM checks 
		WHERE website_id = ? AND checked_at BETWEEN ? AND ?
	`
	
	err := database.DB.QueryRow(uptimeQuery, websiteID, startTime, endTime).Scan(
		&stats.TotalChecks,
		&stats.SuccessfulChecks,
		&stats.FailedChecks,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get uptime stats: %v", err)
	}

	// Calculate uptime percentage
	if stats.TotalChecks > 0 {
		stats.UptimePercentage = (float64(stats.SuccessfulChecks) / float64(stats.TotalChecks)) * 100
	}

	// Get average response time for successful checks
	avgResponseQuery := `
		SELECT AVG(response_time) 
		FROM checks 
		WHERE website_id = ? AND is_up = 1 AND checked_at BETWEEN ? AND ?
	`
	
	var avgResponse *float64
	err = database.DB.QueryRow(avgResponseQuery, websiteID, startTime, endTime).Scan(&avgResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to get average response time: %v", err)
	}
	
	if avgResponse != nil {
		stats.AvgResponseTime = *avgResponse
	}

	// Get the most recent check
	lastCheckQuery := `
		SELECT id, website_id, status_code, response_time, is_up, error_message, checked_at
		FROM checks 
		WHERE website_id = ?
		ORDER BY checked_at DESC
		LIMIT 1
	`
	
	lastCheck := &Check{}
	err = database.DB.QueryRow(lastCheckQuery, websiteID).Scan(
		&lastCheck.ID,
		&lastCheck.WebsiteID,
		&lastCheck.StatusCode,
		&lastCheck.ResponseTime,
		&lastCheck.IsUp,
		&lastCheck.ErrorMessage,
		&lastCheck.CheckedAt,
	)
	
	if err == nil {
		stats.LastCheck = lastCheck
	}
	// If no checks found, LastCheck remains nil

	return stats, nil
}
