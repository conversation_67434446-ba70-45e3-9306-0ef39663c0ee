package services

import (
	"fmt"
	"net/http"
	"time"
	"website-monitor/models"
)

// MonitorService handles website monitoring operations
type MonitorService struct {
	httpClient *http.Client
}

// NewMonitorService creates a new monitoring service instance
func NewMonitorService() *MonitorService {
	return NewMonitorServiceWithTimeout(30 * time.Second)
}

// NewMonitorServiceWithTimeout creates a new monitoring service with custom timeout
func NewMonitorServiceWithTimeout(timeout time.Duration) *MonitorService {
	return &MonitorService{
		httpClient: &http.Client{
			Timeout: timeout,
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				// Allow up to 5 redirects
				if len(via) >= 5 {
					return fmt.Errorf("too many redirects")
				}
				return nil
			},
		},
	}
}

// CheckWebsite performs an HTTP check on a website and returns the result
func (m *MonitorService) CheckWebsite(website *models.Website) (*models.Check, error) {
	check := &models.Check{
		WebsiteID: website.ID,
		CheckedAt: time.Now(),
	}

	// Record start time for response time calculation
	startTime := time.Now()

	// Perform HTTP request
	resp, err := m.httpClient.Get(website.URL)

	// Calculate response time
	responseTime := time.Since(startTime)
	check.ResponseTime = int(responseTime.Nanoseconds() / 1000000) // Convert to milliseconds

	if err != nil {
		// Request failed (timeout, DNS error, connection refused, etc.)
		check.IsUp = false
		check.StatusCode = 0
		check.ErrorMessage = err.Error()
		return check, nil
	}

	defer resp.Body.Close()

	// Set status code
	check.StatusCode = resp.StatusCode

	// Determine if the website is considered "up"
	// Generally, 2xx and 3xx status codes are considered successful
	if resp.StatusCode >= 200 && resp.StatusCode < 400 {
		check.IsUp = true
	} else {
		check.IsUp = false
		check.ErrorMessage = fmt.Sprintf("HTTP %d: %s", resp.StatusCode, resp.Status)
	}

	return check, nil
}

// CheckAndStore performs a website check and stores the result in the database
func (m *MonitorService) CheckAndStore(website *models.Website) error {
	check, err := m.CheckWebsite(website)
	if err != nil {
		return fmt.Errorf("failed to check website %s: %v", website.URL, err)
	}

	// Store the check result
	if err := check.Create(); err != nil {
		return fmt.Errorf("failed to store check result for %s: %v", website.URL, err)
	}

	return nil
}

// IsWebsiteSlow checks if the response time exceeds the alert threshold
func (m *MonitorService) IsWebsiteSlow(website *models.Website, responseTime int) bool {
	return responseTime > website.AlertThreshold
}

// ShouldAlert determines if an alert should be triggered based on recent checks
func (m *MonitorService) ShouldAlert(website *models.Website) (bool, string, error) {
	// Get the last 5 checks to determine alert conditions
	recentChecks, err := models.GetChecksByWebsite(website.ID, 5)
	if err != nil {
		return false, "", fmt.Errorf("failed to get recent checks: %v", err)
	}

	if len(recentChecks) == 0 {
		return false, "", nil
	}

	lastCheck := recentChecks[0]

	// Alert if website is down
	if !lastCheck.IsUp {
		// Check if this is a new downtime (previous check was up)
		if len(recentChecks) > 1 && recentChecks[1].IsUp {
			return true, fmt.Sprintf("Website %s is down (HTTP %d)", website.Name, lastCheck.StatusCode), nil
		}
	}

	// Alert if website is slow
	if lastCheck.IsUp && m.IsWebsiteSlow(website, lastCheck.ResponseTime) {
		return true, fmt.Sprintf("Website %s is slow (%dms response time)", website.Name, lastCheck.ResponseTime), nil
	}

	// Alert if website recovered
	if lastCheck.IsUp && len(recentChecks) > 1 && !recentChecks[1].IsUp {
		return true, fmt.Sprintf("Website %s has recovered", website.Name), nil
	}

	return false, "", nil
}
