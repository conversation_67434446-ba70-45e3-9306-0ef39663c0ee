package database

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"

	_ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB

// InitDatabase initializes the SQLite database connection and creates tables
func InitDatabase() error {
	var dbPath string

	// Check if we're in test mode
	if testPath := os.Getenv("DB_PATH"); testPath != "" {
		dbPath = testPath
	} else {
		// Ensure data directory exists
		dataDir := "data"
		if err := os.MkdirAll(dataDir, 0755); err != nil {
			return fmt.Errorf("failed to create data directory: %v", err)
		}
		dbPath = filepath.Join(dataDir, "monitor.db")
	}

	// Open database connection
	var err error
	DB, err = sql.Open("sqlite3", dbPath+"?_foreign_keys=on")
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}

	// Test connection
	if err = DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	// Run schema migrations
	if err = runMigrations(); err != nil {
		return fmt.Errorf("failed to run migrations: %v", err)
	}

	log.Println("✅ Database initialized successfully")
	return nil
}

// runMigrations executes the SQL schema file
func runMigrations() error {
	// Try different paths for schema file
	schemaPaths := []string{
		filepath.Join("database", "schema.sql"),
		filepath.Join("backend", "database", "schema.sql"),
		filepath.Join("..", "..", "backend", "database", "schema.sql"),
		"schema.sql",
	}

	var schema []byte
	var err error

	for _, schemaPath := range schemaPaths {
		schema, err = ioutil.ReadFile(schemaPath)
		if err == nil {
			break
		}
	}

	if err != nil {
		return fmt.Errorf("failed to read schema file from any location: %v", err)
	}

	if _, err = DB.Exec(string(schema)); err != nil {
		return fmt.Errorf("failed to execute schema: %v", err)
	}

	log.Println("✅ Database schema applied successfully")
	return nil
}

// CloseDatabase closes the database connection
func CloseDatabase() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}
