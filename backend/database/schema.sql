-- Website Monitoring Database Schema

-- Websites table: stores monitored websites
CREATE TABLE IF NOT EXISTS websites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    url TEXT NOT NULL UNIQUE,
    check_interval INTEGER NOT NULL DEFAULT 300, -- seconds (5 minutes default)
    alert_threshold INTEGER NOT NULL DEFAULT 5000, -- response time threshold in ms
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT 1
);

-- Checks table: stores individual monitoring checks
CREATE TABLE IF NOT EXISTS checks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    website_id INTEGER NOT NULL,
    status_code INTEGER,
    response_time INTEGER, -- in milliseconds
    is_up BOOLEAN NOT NULL,
    error_message TEXT,
    checked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIG<PERSON> KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Alerts table: stores alert notifications
CREATE TABLE IF NOT EXISTS alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    website_id INTEGER NOT NULL,
    alert_type TEXT NOT NULL, -- 'down', 'slow', 'recovered'
    message TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    resolved_at DATETIME,
    is_resolved BOOLEAN DEFAULT 0,
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_checks_website_id ON checks(website_id);
CREATE INDEX IF NOT EXISTS idx_checks_checked_at ON checks(checked_at);
CREATE INDEX IF NOT EXISTS idx_alerts_website_id ON alerts(website_id);
CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON alerts(created_at);
CREATE INDEX IF NOT EXISTS idx_websites_is_active ON websites(is_active);

-- Trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_websites_timestamp 
    AFTER UPDATE ON websites
    FOR EACH ROW
BEGIN
    UPDATE websites SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
