#!/bin/bash

echo "=== Website Monitor Start Script ==="

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down server..."
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null
        wait $SERVER_PID 2>/dev/null
        echo "✅ Server stopped gracefully"
    fi
    exit 0
}

# Set trap for CTRL+C
trap cleanup SIGINT SIGTERM

# Check if server is already running on port 8080
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null ; then
    echo "🔄 Port 8080 is in use. Killing existing process..."
    kill $(lsof -t -i:8080) 2>/dev/null
    sleep 2
fi

# Ensure we're in the correct directory
cd "$(dirname "$0")"

echo "📁 Current directory: $(pwd)"
echo "🔧 Building Go backend..."

# Build the Go application
cd backend
go build -o ../bin/website-monitor main.go

if [ $? -ne 0 ]; then
    echo "❌ Failed to build Go application"
    exit 1
fi

cd ..

echo "🚀 Starting website monitor server..."
echo "📊 Dashboard will be available at: http://localhost:8080"
echo "🔍 API endpoints available at: http://localhost:8080/api/"
echo ""
echo "Press CTRL+C to stop the server"
echo "=================================="

# Start the server in background
./bin/website-monitor &
SERVER_PID=$!

# Wait for server to start
sleep 2

# Check if server started successfully
if ! kill -0 $SERVER_PID 2>/dev/null; then
    echo "❌ Failed to start server"
    exit 1
fi

echo "✅ Server started successfully (PID: $SERVER_PID)"
echo "🌐 Open http://localhost:8080 in your browser"

# Keep script running and wait for server
wait $SERVER_PID
