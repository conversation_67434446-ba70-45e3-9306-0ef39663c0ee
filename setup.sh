#!/bin/bash

echo "=== Website Monitor Setup Script ==="
echo "Checking and installing dependencies..."

# Check if Go is installed
if ! command -v go &> /dev/null; then
    echo "❌ Go is not installed. Please install Go 1.19+ from https://golang.org/dl/"
    exit 1
else
    echo "✅ Go is installed: $(go version)"
fi

# Check if Node.js is installed (for Playwright)
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js from https://nodejs.org/"
    exit 1
else
    echo "✅ Node.js is installed: $(node --version)"
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm"
    exit 1
else
    echo "✅ npm is installed: $(npm --version)"
fi

# Navigate to backend directory and install Go dependencies
echo "📦 Installing Go dependencies..."
cd backend
go mod tidy

# Add required Go packages
echo "📦 Adding Go dependencies..."
go get github.com/gorilla/mux
go get github.com/gorilla/handlers
go get github.com/mattn/go-sqlite3
go get github.com/rs/cors

echo "📦 Installing Playwright for testing..."
cd ../tests/frontend
npm init -y
npm install @playwright/test
npx playwright install

echo "🗄️  Setting up database..."
cd ../../
mkdir -p data

echo "✅ Setup completed successfully!"
echo "📋 Next steps:"
echo "   1. Run './start.sh' to start the application"
echo "   2. Open http://localhost:8080 in your browser"
