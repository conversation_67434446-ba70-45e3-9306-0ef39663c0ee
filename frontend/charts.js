// Charts functionality for Website Monitor Dashboard

class ChartManager {
    constructor() {
        this.charts = {};
        this.init();
    }

    init() {
        // Set Chart.js defaults
        Chart.defaults.font.family = '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, sans-serif';
        Chart.defaults.color = '#4a5568';
        Chart.defaults.borderColor = '#e2e8f0';
        Chart.defaults.backgroundColor = 'rgba(66, 153, 225, 0.1)';
    }

    createResponseTimeChart(canvasId, websiteId) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Response Time (ms)',
                    data: [],
                    borderColor: '#4299e1',
                    backgroundColor: 'rgba(66, 153, 225, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#4299e1',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Response Time Trend (Last 24 Hours)',
                        font: {
                            size: 16,
                            weight: '600'
                        },
                        padding: 20
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#4299e1',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                return `Response Time: ${context.parsed.y}ms`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Time',
                            font: {
                                weight: '500'
                            }
                        },
                        grid: {
                            color: 'rgba(226, 232, 240, 0.5)'
                        },
                        ticks: {
                            maxTicksLimit: 8
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Response Time (ms)',
                            font: {
                                weight: '500'
                            }
                        },
                        grid: {
                            color: 'rgba(226, 232, 240, 0.5)'
                        },
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + 'ms';
                            }
                        }
                    }
                },
                elements: {
                    point: {
                        hoverBackgroundColor: '#4299e1'
                    }
                }
            }
        });

        this.charts[canvasId] = chart;
        
        // Load data for the chart
        if (websiteId) {
            this.loadResponseTimeData(websiteId, chart);
        }

        return chart;
    }

    async loadResponseTimeData(websiteId, chart) {
        try {
            const response = await fetch(`/api/websites/${websiteId}/checks?limit=50`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const checks = await response.json();
            
            // Filter successful checks and reverse to show chronological order
            const successfulChecks = checks
                .filter(check => check.is_up && check.response_time > 0)
                .reverse();

            if (successfulChecks.length === 0) {
                this.showNoDataMessage(chart);
                return;
            }

            // Prepare data for chart
            const labels = successfulChecks.map(check => {
                const date = new Date(check.checked_at);
                return this.formatChartTime(date);
            });

            const data = successfulChecks.map(check => check.response_time);

            // Update chart data
            chart.data.labels = labels;
            chart.data.datasets[0].data = data;
            chart.update('none'); // Update without animation for better performance

        } catch (error) {
            console.error('Failed to load response time data:', error);
            this.showErrorMessage(chart);
        }
    }

    createUptimeChart(canvasId, websiteId) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return null;

        const ctx = canvas.getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Uptime', 'Downtime'],
                datasets: [{
                    data: [0, 0],
                    backgroundColor: [
                        '#48bb78',
                        '#e53e3e'
                    ],
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Uptime (Last 24 Hours)',
                        font: {
                            size: 16,
                            weight: '600'
                        },
                        padding: 20
                    },
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#4299e1',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label;
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                                return `${label}: ${percentage}%`;
                            }
                        }
                    }
                }
            }
        });

        this.charts[canvasId] = chart;
        
        // Load data for the chart
        if (websiteId) {
            this.loadUptimeData(websiteId, chart);
        }

        return chart;
    }

    async loadUptimeData(websiteId, chart) {
        try {
            const response = await fetch(`/api/websites/${websiteId}/stats?hours=24`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const stats = await response.json();
            
            const uptimePercentage = stats.uptime_percentage || 0;
            const downtimePercentage = 100 - uptimePercentage;

            // Update chart data
            chart.data.datasets[0].data = [uptimePercentage, downtimePercentage];
            chart.update('none');

        } catch (error) {
            console.error('Failed to load uptime data:', error);
            this.showErrorMessage(chart);
        }
    }

    showNoDataMessage(chart) {
        // Add a text overlay to show "No data available"
        const canvas = chart.canvas;
        const ctx = canvas.getContext('2d');
        
        chart.data.labels = [];
        chart.data.datasets[0].data = [];
        chart.update('none');
        
        // Draw "No data" message
        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto';
        ctx.fillStyle = '#718096';
        ctx.fillText('No data available', canvas.width / 2, canvas.height / 2);
        ctx.restore();
    }

    showErrorMessage(chart) {
        // Add a text overlay to show error message
        const canvas = chart.canvas;
        const ctx = canvas.getContext('2d');
        
        chart.data.labels = [];
        chart.data.datasets[0].data = [];
        chart.update('none');
        
        // Draw error message
        ctx.save();
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto';
        ctx.fillStyle = '#e53e3e';
        ctx.fillText('Failed to load data', canvas.width / 2, canvas.height / 2);
        ctx.restore();
    }

    formatChartTime(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffHours = diffMs / (1000 * 60 * 60);

        if (diffHours < 1) {
            return date.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        } else if (diffHours < 24) {
            return date.toLocaleTimeString('en-US', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        } else {
            return date.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric',
                hour: '2-digit'
            });
        }
    }

    destroyChart(canvasId) {
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
            delete this.charts[canvasId];
        }
    }

    destroyAllCharts() {
        Object.keys(this.charts).forEach(canvasId => {
            this.destroyChart(canvasId);
        });
    }
}

// Initialize chart manager
let chartManager;

document.addEventListener('DOMContentLoaded', () => {
    chartManager = new ChartManager();
});

// Override the showWebsiteDetails function to include chart creation
const originalShowWebsiteDetails = WebsiteMonitor.prototype.showWebsiteDetails;
WebsiteMonitor.prototype.showWebsiteDetails = function(website) {
    // Call original function
    originalShowWebsiteDetails.call(this, website);
    
    // Create chart after modal is shown
    setTimeout(() => {
        if (chartManager) {
            chartManager.createResponseTimeChart('response-time-chart', website.id);
        }
    }, 100);
};
