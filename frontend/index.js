// Website Monitor Dashboard JavaScript

class WebsiteMonitor {
    constructor() {
        this.websites = [];
        this.refreshInterval = null;
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.loadWebsites();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Add website button
        document.getElementById('add-website-btn').addEventListener('click', () => {
            this.showAddWebsiteModal();
        });

        // Refresh button
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadWebsites();
        });

        // Add website form
        document.getElementById('add-website-form').addEventListener('submit', (e) => {
            this.handleAddWebsite(e);
        });

        // Modal close events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.hideAllModals();
            }
        });

        // Escape key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAllModals();
            }
        });
    }

    async loadWebsites() {
        try {
            this.showLoading();
            const response = await fetch('/api/websites');
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            this.websites = await response.json();
            await this.loadWebsiteStats();
            this.renderWebsites();
            this.updateSummaryCards();
            
        } catch (error) {
            console.error('Failed to load websites:', error);
            this.showToast('Failed to load websites', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async loadWebsiteStats() {
        const statsPromises = this.websites.map(async (website) => {
            try {
                const [statsResponse, checksResponse] = await Promise.all([
                    fetch(`/api/websites/${website.id}/stats?hours=24`),
                    fetch(`/api/websites/${website.id}/checks?limit=1`)
                ]);

                if (statsResponse.ok) {
                    website.stats = await statsResponse.json();
                }

                if (checksResponse.ok) {
                    const checks = await checksResponse.json();
                    website.lastCheck = checks.length > 0 ? checks[0] : null;
                }
            } catch (error) {
                console.error(`Failed to load stats for website ${website.id}:`, error);
            }
        });

        await Promise.all(statsPromises);
    }

    renderWebsites() {
        const grid = document.getElementById('websites-grid');
        const noWebsites = document.getElementById('no-websites');

        if (this.websites.length === 0) {
            grid.style.display = 'none';
            noWebsites.style.display = 'block';
            return;
        }

        grid.style.display = 'grid';
        noWebsites.style.display = 'none';

        grid.innerHTML = this.websites.map(website => this.createWebsiteCard(website)).join('');

        // Add click listeners to website cards
        grid.querySelectorAll('.website-card').forEach((card, index) => {
            card.addEventListener('click', () => {
                this.showWebsiteDetails(this.websites[index]);
            });
        });
    }

    createWebsiteCard(website) {
        const lastCheck = website.lastCheck;
        const stats = website.stats || {};
        
        const status = lastCheck ? (lastCheck.is_up ? 'up' : 'down') : 'unknown';
        const statusText = lastCheck ? (lastCheck.is_up ? 'Online' : 'Offline') : 'Unknown';
        const responseTime = lastCheck ? `${lastCheck.response_time}ms` : 'N/A';
        const uptime = stats.uptime_percentage ? `${stats.uptime_percentage.toFixed(1)}%` : 'N/A';
        const lastCheckTime = lastCheck ? this.formatTimeAgo(new Date(lastCheck.checked_at)) : 'Never';

        return `
            <div class="website-card" data-website-id="${website.id}">
                <div class="website-header">
                    <div class="website-info">
                        <h3>${this.escapeHtml(website.name)}</h3>
                        <a href="${website.url}" target="_blank" class="website-url" onclick="event.stopPropagation()">
                            ${this.escapeHtml(website.url)}
                        </a>
                    </div>
                    <span class="status-badge status-${status}">${statusText}</span>
                </div>
                <div class="website-stats">
                    <div class="stat">
                        <span class="stat-value">${responseTime}</span>
                        <span class="stat-label">Response Time</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">${uptime}</span>
                        <span class="stat-label">Uptime (24h)</span>
                    </div>
                </div>
                <div style="margin-top: 1rem; font-size: 0.8rem; color: #718096;">
                    Last checked: ${lastCheckTime}
                </div>
            </div>
        `;
    }

    updateSummaryCards() {
        const totalSites = this.websites.length;
        let sitesUp = 0;
        let sitesDown = 0;
        let totalResponseTime = 0;
        let totalUptime = 0;
        let sitesWithData = 0;

        this.websites.forEach(website => {
            if (website.lastCheck) {
                if (website.lastCheck.is_up) {
                    sitesUp++;
                    totalResponseTime += website.lastCheck.response_time;
                } else {
                    sitesDown++;
                }
            }

            if (website.stats && website.stats.uptime_percentage !== undefined) {
                totalUptime += website.stats.uptime_percentage;
                sitesWithData++;
            }
        });

        const avgResponseTime = sitesUp > 0 ? Math.round(totalResponseTime / sitesUp) : 0;
        const avgUptime = sitesWithData > 0 ? (totalUptime / sitesWithData).toFixed(1) : 0;

        document.getElementById('total-up').textContent = sitesUp;
        document.getElementById('total-down').textContent = sitesDown;
        document.getElementById('avg-response').textContent = `${avgResponseTime}ms`;
        document.getElementById('avg-uptime').textContent = `${avgUptime}%`;
    }

    showAddWebsiteModal() {
        const modal = document.getElementById('add-website-modal');
        modal.classList.add('show');
        modal.style.display = 'flex';
        
        // Focus on first input
        setTimeout(() => {
            document.getElementById('website-name').focus();
        }, 100);
    }

    hideAddWebsiteModal() {
        const modal = document.getElementById('add-website-modal');
        modal.classList.remove('show');
        modal.style.display = 'none';
        
        // Reset form
        document.getElementById('add-website-form').reset();
    }

    async handleAddWebsite(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const websiteData = {
            name: formData.get('name'),
            url: formData.get('url'),
            check_interval: parseInt(formData.get('check_interval')),
            alert_threshold: parseInt(formData.get('alert_threshold'))
        };

        try {
            this.showLoading();
            
            const response = await fetch('/api/websites', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(websiteData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(errorText || `HTTP error! status: ${response.status}`);
            }

            const newWebsite = await response.json();
            this.showToast(`Website "${newWebsite.name}" added successfully!`, 'success');
            this.hideAddWebsiteModal();
            await this.loadWebsites();

        } catch (error) {
            console.error('Failed to add website:', error);
            this.showToast(`Failed to add website: ${error.message}`, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async showWebsiteDetails(website) {
        const modal = document.getElementById('website-details-modal');
        document.getElementById('details-website-name').textContent = website.name;
        
        // Load detailed stats and checks
        try {
            const [statsResponse, checksResponse] = await Promise.all([
                fetch(`/api/websites/${website.id}/stats?hours=24`),
                fetch(`/api/websites/${website.id}/checks?limit=20`)
            ]);

            if (statsResponse.ok) {
                const stats = await statsResponse.json();
                this.updateDetailsOverview(website, stats);
            }

            if (checksResponse.ok) {
                const checks = await checksResponse.json();
                this.updateDetailsChecks(checks);
            }

        } catch (error) {
            console.error('Failed to load website details:', error);
        }

        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    updateDetailsOverview(website, stats) {
        const lastCheck = stats.last_check;
        
        document.getElementById('details-status').textContent = 
            lastCheck ? (lastCheck.is_up ? 'Online' : 'Offline') : 'Unknown';
        document.getElementById('details-status').className = 
            `status-badge status-${lastCheck ? (lastCheck.is_up ? 'up' : 'down') : 'unknown'}`;
        
        document.getElementById('details-uptime').textContent = 
            `${stats.uptime_percentage ? stats.uptime_percentage.toFixed(1) : 0}%`;
        
        document.getElementById('details-response-time').textContent = 
            `${stats.avg_response_time ? Math.round(stats.avg_response_time) : 0}ms`;
        
        document.getElementById('details-last-check').textContent = 
            lastCheck ? this.formatTimeAgo(new Date(lastCheck.checked_at)) : 'Never';
    }

    updateDetailsChecks(checks) {
        const container = document.getElementById('recent-checks-list');
        
        if (checks.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #718096;">No checks available</p>';
            return;
        }

        container.innerHTML = checks.map(check => `
            <div class="check-item" style="display: flex; justify-content: space-between; align-items: center; padding: 1rem; border-bottom: 1px solid #e2e8f0;">
                <div>
                    <span class="status-badge status-${check.is_up ? 'up' : 'down'}">
                        ${check.is_up ? 'Online' : 'Offline'}
                    </span>
                    ${check.error_message ? `<div style="color: #e53e3e; font-size: 0.9rem; margin-top: 0.25rem;">${this.escapeHtml(check.error_message)}</div>` : ''}
                </div>
                <div style="text-align: right;">
                    <div>${check.is_up ? `${check.response_time}ms` : 'N/A'}</div>
                    <div style="font-size: 0.8rem; color: #718096;">${this.formatTimeAgo(new Date(check.checked_at))}</div>
                </div>
            </div>
        `).join('');
    }

    hideWebsiteDetailsModal() {
        const modal = document.getElementById('website-details-modal');
        modal.classList.remove('show');
        modal.style.display = 'none';
    }

    hideAllModals() {
        this.hideAddWebsiteModal();
        this.hideWebsiteDetailsModal();
    }

    showLoading() {
        document.getElementById('loading-overlay').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loading-overlay').style.display = 'none';
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        container.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }

    startAutoRefresh() {
        // Refresh every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.loadWebsites();
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    formatTimeAgo(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        return `${diffDays}d ago`;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Global functions for modal controls
function showAddWebsiteModal() {
    window.monitor.showAddWebsiteModal();
}

function hideAddWebsiteModal() {
    window.monitor.hideAddWebsiteModal();
}

function hideWebsiteDetailsModal() {
    window.monitor.hideWebsiteDetailsModal();
}

function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected tab
    document.getElementById(`${tabName}-tab`).classList.add('active');
    event.target.classList.add('active');
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.monitor = new WebsiteMonitor();
});
