<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Monitor Dashboard</title>
    <link rel="stylesheet" href="index.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-chart-line"></i> Website Monitor</h1>
                <div class="header-actions">
                    <button id="add-website-btn" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Website
                    </button>
                    <button id="refresh-btn" class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Summary Cards -->
            <section class="summary-section">
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-icon green">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="total-up">0</h3>
                            <p>Sites Up</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon red">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="total-down">0</h3>
                            <p>Sites Down</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon blue">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="avg-response">0ms</h3>
                            <p>Avg Response</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon orange">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="avg-uptime">0%</h3>
                            <p>Avg Uptime</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Websites Grid -->
            <section class="websites-section">
                <h2>Monitored Websites</h2>
                <div id="websites-grid" class="websites-grid">
                    <!-- Website cards will be dynamically inserted here -->
                </div>
                <div id="no-websites" class="no-websites" style="display: none;">
                    <i class="fas fa-globe"></i>
                    <h3>No websites being monitored</h3>
                    <p>Add your first website to start monitoring</p>
                    <button class="btn btn-primary" onclick="showAddWebsiteModal()">
                        <i class="fas fa-plus"></i> Add Website
                    </button>
                </div>
            </section>
        </main>
    </div>

    <!-- Add Website Modal -->
    <div id="add-website-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Add New Website</h2>
                <button class="modal-close" onclick="hideAddWebsiteModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="add-website-form" class="modal-body">
                <div class="form-group">
                    <label for="website-name">Website Name</label>
                    <input type="text" id="website-name" name="name" required 
                           placeholder="e.g., My Blog">
                </div>
                <div class="form-group">
                    <label for="website-url">Website URL</label>
                    <input type="url" id="website-url" name="url" required 
                           placeholder="https://example.com">
                </div>
                <div class="form-group">
                    <label for="check-interval">Check Interval</label>
                    <select id="check-interval" name="check_interval">
                        <option value="60">1 minute</option>
                        <option value="300" selected>5 minutes</option>
                        <option value="900">15 minutes</option>
                        <option value="1800">30 minutes</option>
                        <option value="3600">1 hour</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="alert-threshold">Alert Threshold (ms)</label>
                    <input type="number" id="alert-threshold" name="alert_threshold" 
                           value="5000" min="100" step="100"
                           placeholder="5000">
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="hideAddWebsiteModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Website
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Website Details Modal -->
    <div id="website-details-modal" class="modal">
        <div class="modal-content large">
            <div class="modal-header">
                <h2 id="details-website-name">Website Details</h2>
                <button class="modal-close" onclick="hideWebsiteDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="details-tabs">
                    <button class="tab-btn active" onclick="showTab('overview')">Overview</button>
                    <button class="tab-btn" onclick="showTab('checks')">Recent Checks</button>
                    <button class="tab-btn" onclick="showTab('settings')">Settings</button>
                </div>
                
                <div id="overview-tab" class="tab-content active">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <label>Status</label>
                            <span id="details-status" class="status-badge">Unknown</span>
                        </div>
                        <div class="stat-item">
                            <label>Uptime (24h)</label>
                            <span id="details-uptime">0%</span>
                        </div>
                        <div class="stat-item">
                            <label>Avg Response Time</label>
                            <span id="details-response-time">0ms</span>
                        </div>
                        <div class="stat-item">
                            <label>Last Check</label>
                            <span id="details-last-check">Never</span>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="response-time-chart"></canvas>
                    </div>
                </div>
                
                <div id="checks-tab" class="tab-content">
                    <div id="recent-checks-list" class="checks-list">
                        <!-- Recent checks will be loaded here -->
                    </div>
                </div>
                
                <div id="settings-tab" class="tab-content">
                    <form id="edit-website-form">
                        <!-- Website settings form will be loaded here -->
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Loading...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="index.js"></script>
    <script src="charts.js"></script>
</body>
</html>
